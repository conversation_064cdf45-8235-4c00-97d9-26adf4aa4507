/**
 * Database setup script for local development
 * Initializes the user table schema
 */

import dotenv from 'dotenv';
import { userService } from './services/userService.mjs';

// Load environment variables
dotenv.config();

async function setupDatabase() {
  try {
    console.log('🔧 Setting up database schema...');
    
    // Initialize the database schema
    await userService.initializeSchema();
    
    console.log('✅ Database schema initialized successfully!');
    console.log('\n📊 Database Configuration:');
    console.log(`   Host: ${process.env.DB_HOST}`);
    console.log(`   Database: ${process.env.DB_NAME}`);
    console.log(`   Username: ${process.env.DB_USERNAME}`);
    console.log(`   SSL: ${process.env.DB_SSL}`);
    
    // Test the connection by trying to get users
    console.log('\n🧪 Testing database connection...');
    const users = await userService.getAllUsers(5);
    console.log(`✅ Connection successful! Found ${users.length} existing users.`);
    
    if (users.length > 0) {
      console.log('\n👥 Existing users:');
      users.forEach(user => {
        console.log(`   - ${user.phoneNumber}${user.name ? ` (${user.name})` : ''}`);
      });
    }
    
    // Close the connection
    await userService.close();
    
    console.log('\n🎉 Database setup completed successfully!');
    console.log('You can now run: npm start');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error);
    console.error('\n🔍 Troubleshooting:');
    console.error('1. Check your database credentials in .env file');
    console.error('2. Ensure your database server is running');
    console.error('3. Verify network connectivity to the database');
    process.exit(1);
  }
}

// Run the setup
setupDatabase();
