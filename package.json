{"name": "cravin-concierge-whatsapp-lambda", "version": "1.0.0", "description": "WhatsApp webhook Lambda functions for Cravin Concierge with AI agent integration", "main": "index.mjs", "type": "module", "scripts": {"setup": "node setup-db.mjs", "start": "node local-server.mjs", "dev": "nodemon local-server.mjs", "test": "echo \"Error: no test specified\" && exit 1", "deploy": "zip -r function.zip . && aws lambda update-function-code --function-name cravin-whatsapp-webhook --zip-file fileb://function.zip"}, "keywords": ["whatsapp", "lambda", "aws", "bedrock", "ai", "vector-search", "postgresql"], "author": "Cravin Concierge", "license": "ISC", "dependencies": {"@aws-sdk/client-bedrock-runtime": "^3.600.0", "@aws-sdk/client-bedrock-agent-runtime": "^3.600.0", "pg": "^8.11.3", "axios": "^1.6.8", "crypto": "^1.0.1", "express": "^4.18.2", "dotenv": "^16.4.5"}, "devDependencies": {"@types/node": "^20.12.7", "@types/pg": "^8.11.6", "nodemon": "^3.1.0"}}