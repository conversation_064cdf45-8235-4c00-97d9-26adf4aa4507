/**
 * Security utilities for webhook validation
 */

import crypto from 'crypto';
import { config } from '../config/environment.mjs';

/**
 * Validates WhatsApp webhook signature
 * @param {Object} event - Lambda event object
 * @returns {boolean} True if signature is valid
 */
export function validateWebhookSignature(event) {
  try {
    // Skip validation if no webhook secret is configured
    if (!config.whatsapp.webhookSecret) {
      console.warn('No webhook secret configured, skipping signature validation');
      return true;
    }

    const signature = event.headers['x-hub-signature-256'] || event.headers['X-Hub-Signature-256'];
    
    if (!signature) {
      console.error('No signature found in headers');
      return false;
    }

    // Remove 'sha256=' prefix
    const signatureHash = signature.replace('sha256=', '');
    
    // Calculate expected signature
    const expectedSignature = crypto
      .createHmac('sha256', config.whatsapp.webhookSecret)
      .update(event.body)
      .digest('hex');

    // Compare signatures using timing-safe comparison
    const isValid = crypto.timingSafeEqual(
      Buffer.from(signatureHash, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );

    if (!isValid) {
      console.error('Webhook signature validation failed');
    }

    return isValid;

  } catch (error) {
    console.error('Error validating webhook signature:', error);
    return false;
  }
}

/**
 * Sanitizes user input to prevent injection attacks
 * @param {string} input - User input to sanitize
 * @returns {string} Sanitized input
 */
export function sanitizeInput(input) {
  if (typeof input !== 'string') {
    return '';
  }

  // Remove potentially dangerous characters and limit length
  return input
    .replace(/[<>\"'&]/g, '') // Remove HTML/XML characters
    .replace(/[\x00-\x1f\x7f-\x9f]/g, '') // Remove control characters
    .trim()
    .substring(0, 1000); // Limit length
}

/**
 * Validates phone number format
 * @param {string} phoneNumber - Phone number to validate
 * @returns {boolean} True if valid format
 */
export function isValidPhoneNumber(phoneNumber) {
  // Basic validation for international phone numbers
  const phoneRegex = /^\+?[1-9]\d{1,14}$/;
  return phoneRegex.test(phoneNumber);
}

/**
 * Rate limiting check (simple in-memory implementation)
 * In production, use Redis or DynamoDB for distributed rate limiting
 */
const rateLimitStore = new Map();

/**
 * Checks if user has exceeded rate limit
 * @param {string} userId - User identifier (phone number)
 * @param {number} maxRequests - Maximum requests allowed
 * @param {number} windowMs - Time window in milliseconds
 * @returns {boolean} True if rate limit exceeded
 */
export function isRateLimited(userId, maxRequests = 10, windowMs = 60000) {
  const now = Date.now();
  const userRequests = rateLimitStore.get(userId) || [];
  
  // Remove old requests outside the time window
  const validRequests = userRequests.filter(timestamp => now - timestamp < windowMs);
  
  // Check if limit exceeded
  if (validRequests.length >= maxRequests) {
    return true;
  }
  
  // Add current request
  validRequests.push(now);
  rateLimitStore.set(userId, validRequests);
  
  return false;
}

/**
 * Cleans up old rate limit entries
 * Should be called periodically to prevent memory leaks
 */
export function cleanupRateLimit() {
  const now = Date.now();
  const oneHour = 60 * 60 * 1000;
  
  for (const [userId, requests] of rateLimitStore.entries()) {
    const validRequests = requests.filter(timestamp => now - timestamp < oneHour);
    
    if (validRequests.length === 0) {
      rateLimitStore.delete(userId);
    } else {
      rateLimitStore.set(userId, validRequests);
    }
  }
}
