/**
 * Local development server for testing WhatsApp Lambda functions
 * Simulates AWS Lambda environment for local testing
 */

import express from 'express';
import { handler } from './index.mjs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Add request logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  console.log('Headers:', JSON.stringify(req.headers, null, 2));
  if (req.body && Object.keys(req.body).length > 0) {
    console.log('Body:', JSON.stringify(req.body, null, 2));
  }
  if (req.query && Object.keys(req.query).length > 0) {
    console.log('Query:', JSON.stringify(req.query, null, 2));
  }
  next();
});

// Webhook endpoint - handles both GET (verification) and POST (messages)
app.all('/webhook', async (req, res) => {
  try {
    // Create Lambda-like event object
    const event = {
      httpMethod: req.method,
      headers: req.headers,
      queryStringParameters: req.query,
      body: req.method === 'POST' ? JSON.stringify(req.body) : null,
      requestContext: {
        http: {
          method: req.method
        }
      }
    };

    console.log('\n=== Lambda Event ===');
    console.log(JSON.stringify(event, null, 2));
    console.log('==================\n');

    // Call the Lambda handler
    const result = await handler(event);

    console.log('\n=== Lambda Response ===');
    console.log(JSON.stringify(result, null, 2));
    console.log('=====================\n');

    // Send response
    res.status(result.statusCode);
    
    if (result.headers) {
      Object.entries(result.headers).forEach(([key, value]) => {
        res.set(key, value);
      });
    }

    // For webhook verification, send plain text
    if (req.method === 'GET' && result.statusCode === 200) {
      res.send(result.body);
    } else {
      res.json(JSON.parse(result.body));
    }

  } catch (error) {
    console.error('Error in webhook handler:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Test endpoint for manual message testing
app.get('/test', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
        <title>WhatsApp Webhook Tester</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 600px; }
            textarea { width: 100%; height: 200px; margin: 10px 0; }
            button { padding: 10px 20px; margin: 5px; }
            .response { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>WhatsApp Webhook Tester</h1>
            
            <h3>Test Webhook Verification (GET)</h3>
            <button onclick="testVerification()">Test Verification</button>
            
            <h3>Test Message Processing (POST)</h3>
            <textarea id="messagePayload" placeholder="Enter WhatsApp message payload JSON here...">
{
  "entry": [{
    "changes": [{
      "field": "messages",
      "value": {
        "messages": [{
          "from": "1234567890",
          "id": "msg_123",
          "type": "text",
          "text": {
            "body": "Hello, my name is John. I'm looking for Italian restaurants"
          }
        }]
      }
    }]
  }]
}
            </textarea>
            <button onclick="testMessage()">Send Test Message</button>
            
            <div id="response" class="response" style="display: none;">
                <h4>Response:</h4>
                <pre id="responseContent"></pre>
            </div>
        </div>

        <script>
            async function testVerification() {
                try {
                    const response = await fetch('/webhook?hub.mode=subscribe&hub.verify_token=${process.env.WHATSAPP_VERIFY_TOKEN}&hub.challenge=test_challenge');
                    const result = await response.text();
                    showResponse('Verification Response: ' + result);
                } catch (error) {
                    showResponse('Error: ' + error.message);
                }
            }

            async function testMessage() {
                try {
                    const payload = document.getElementById('messagePayload').value;
                    const response = await fetch('/webhook', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Hub-Signature-256': 'sha256=test_signature'
                        },
                        body: payload
                    });
                    const result = await response.json();
                    showResponse(JSON.stringify(result, null, 2));
                } catch (error) {
                    showResponse('Error: ' + error.message);
                }
            }

            function showResponse(content) {
                document.getElementById('responseContent').textContent = content;
                document.getElementById('response').style.display = 'block';
            }
        </script>
    </body>
    </html>
  `);
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Local WhatsApp webhook server running on http://localhost:${PORT}`);
  console.log(`📱 Webhook URL: http://localhost:${PORT}/webhook`);
  console.log(`🧪 Test interface: http://localhost:${PORT}/test`);
  console.log(`❤️  Health check: http://localhost:${PORT}/health`);
  console.log('\n📋 Environment Configuration:');
  console.log(`   - WhatsApp Verify Token: ${process.env.WHATSAPP_VERIFY_TOKEN ? '✅ Set' : '❌ Missing'}`);
  console.log(`   - WhatsApp Access Token: ${process.env.WHATSAPP_ACCESS_TOKEN ? '✅ Set' : '❌ Missing'}`);
  console.log(`   - Database Host: ${process.env.DB_HOST ? '✅ Set' : '❌ Missing'}`);
  console.log(`   - AWS Region: ${process.env.AWS_REGION || 'us-east-1'}`);
  console.log(`   - Bedrock Agent ID: ${process.env.BEDROCK_AGENT_ID ? '✅ Set' : '❌ Missing'}`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down server...');
  process.exit(0);
});
