/**
 * WhatsApp Business API Service
 * Handles sending messages and managing WhatsApp interactions
 */

import axios from 'axios';
import { config } from '../config/environment.mjs';

class WhatsAppService {
  constructor() {
    this.baseURL = `https://graph.facebook.com/${config.whatsapp.apiVersion}/${config.whatsapp.phoneNumberId}`;
    this.accessToken = config.whatsapp.accessToken;
  }

  /**
   * Sends a text message to a WhatsApp user
   * @param {string} to - Recipient phone number
   * @param {string} message - Message text to send
   * @returns {Object} API response
   */
  async sendTextMessage(to, message) {
    try {
      console.log(`Sending message to ${to}: ${message}`);

      const payload = {
        messaging_product: 'whatsapp',
        to: to,
        type: 'text',
        text: {
          body: message
        }
      };

      const response = await axios.post(
        `${this.baseURL}/messages`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Message sent successfully:', response.data);
      return response.data;

    } catch (error) {
      console.error('Error sending WhatsApp message:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Sends a typing indicator to show the bot is processing
   * @param {string} to - Recipient phone number
   */
  async sendTypingIndicator(to) {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        recipient_type: 'individual',
        to: to,
        type: 'text',
        text: {
          body: '...'
        }
      };

      await axios.post(
        `${this.baseURL}/messages`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

    } catch (error) {
      console.error('Error sending typing indicator:', error.response?.data || error.message);
      // Don't throw error for typing indicator failures
    }
  }

  /**
   * Marks a message as read
   * @param {string} messageId - ID of the message to mark as read
   */
  async markMessageAsRead(messageId) {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        status: 'read',
        message_id: messageId
      };

      await axios.post(
        `${this.baseURL}/messages`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log(`Message ${messageId} marked as read`);

    } catch (error) {
      console.error('Error marking message as read:', error.response?.data || error.message);
      // Don't throw error for read receipt failures
    }
  }

  /**
   * Sends a template message
   * @param {string} to - Recipient phone number
   * @param {string} templateName - Name of the template
   * @param {string} languageCode - Language code (e.g., 'en_US')
   * @param {Array} components - Template components/parameters
   */
  async sendTemplateMessage(to, templateName, languageCode = 'en_US', components = []) {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to: to,
        type: 'template',
        template: {
          name: templateName,
          language: {
            code: languageCode
          },
          components: components
        }
      };

      const response = await axios.post(
        `${this.baseURL}/messages`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Template message sent successfully:', response.data);
      return response.data;

    } catch (error) {
      console.error('Error sending template message:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Sends a message with quick reply buttons
   * @param {string} to - Recipient phone number
   * @param {string} bodyText - Main message text
   * @param {Array} buttons - Array of button objects
   */
  async sendInteractiveMessage(to, bodyText, buttons) {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to: to,
        type: 'interactive',
        interactive: {
          type: 'button',
          body: {
            text: bodyText
          },
          action: {
            buttons: buttons.map((button, index) => ({
              type: 'reply',
              reply: {
                id: `btn_${index}`,
                title: button.title
              }
            }))
          }
        }
      };

      const response = await axios.post(
        `${this.baseURL}/messages`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Interactive message sent successfully:', response.data);
      return response.data;

    } catch (error) {
      console.error('Error sending interactive message:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Gets media URL from media ID
   * @param {string} mediaId - Media ID from WhatsApp
   * @returns {string} Media URL
   */
  async getMediaUrl(mediaId) {
    try {
      const response = await axios.get(
        `https://graph.facebook.com/${config.whatsapp.apiVersion}/${mediaId}`,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`
          }
        }
      );

      return response.data.url;

    } catch (error) {
      console.error('Error getting media URL:', error.response?.data || error.message);
      throw error;
    }
  }
}

export const whatsappService = new WhatsAppService();
