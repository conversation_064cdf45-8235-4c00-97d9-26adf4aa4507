/**
 * Main Lambda handler for WhatsApp webhook
 * Routes GET requests to webhook verification and POST requests to message handling
 */

import { handleWebhookVerification } from './handlers/webhookVerification.mjs';
import { handleIncomingMessage } from './handlers/messageHandler.mjs';
import { validateConfig } from './config/environment.mjs';
// Security utilities available for future use
// import { isRateLimited, sanitizeInput } from './utils/security.mjs';

export const handler = async (event) => {
  try {
    console.log('Lambda invoked with event:', JSON.stringify(event, null, 2));

    // Validate environment configuration
    validateConfig();

    // Extract HTTP method
    const httpMethod = event.httpMethod || event.requestContext?.http?.method;

    if (!httpMethod) {
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          error: 'Bad Request',
          message: 'HTTP method not found'
        })
      };
    }

    // Route based on HTTP method
    switch (httpMethod.toUpperCase()) {
      case 'GET':
        // Handle webhook verification
        return await handleWebhookVerification(event);

      case 'POST':
        // Handle incoming messages
        return await handleIncomingMessage(event);

      default:
        return {
          statusCode: 405,
          headers: {
            'Content-Type': 'application/json',
            'Allow': 'GET, POST'
          },
          body: JSON.stringify({
            error: 'Method Not Allowed',
            message: `HTTP method ${httpMethod} is not supported`
          })
        };
    }

  } catch (error) {
    console.error('Unhandled error in Lambda handler:', error);

    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        error: 'Internal Server Error',
        message: 'An unexpected error occurred'
      })
    };
  }
};
  